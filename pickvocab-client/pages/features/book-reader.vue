<script setup lang="ts">
import {
  Fwb<PERSON><PERSON><PERSON>,
  FwbNavbar<PERSON>oll<PERSON>se,
  FwbButton,
} from "flowbite-vue";
import HomeFooter from "~/components/home/<USER>";
import HomeNavbar from '~/components/home/<USER>';
// @ts-ignore
import IconCheck from '@tabler/icons-vue/dist/esm/icons/IconCheck.mjs';
// @ts-ignore
import IconBook from '@tabler/icons-vue/dist/esm/icons/IconBook.mjs';
// @ts-ignore
import IconBulb from '@tabler/icons-vue/dist/esm/icons/IconBulb.mjs';
// @ts-ignore
import IconAffiliate from '@tabler/icons-vue/dist/esm/icons/IconAffiliate.mjs';
// @ts-ignore
import IconBookmark from '@tabler/icons-vue/dist/esm/icons/IconBookmark.mjs';
// @ts-ignore
import IconStar from '@tabler/icons-vue/dist/esm/icons/IconStar.mjs';

useSeoMeta({
  title: 'Book Reader with AI Dictionary | Pickvocab',
  description: 'Immerse yourself in English books with an integrated AI dictionary. Effortlessly save new vocabulary in context with a single click. Supports EPUB format.',
  ogTitle: 'Book Reader with AI Dictionary',
  keywords: 'EPUB reader with dictionary, learn English through reading, save vocabulary from books, contextual learning, AI book assistant'
});

const appReaderUrl = '/app/reader';
</script>

<template>
  <div>
    <HomeNavbar />

    <!-- Hero Section -->
    <div class="flex flex-col items-center mt-16 md:mt-24 py-10 px-4 md:px-8">
      <h1 class="mb-4 text-3xl font-extrabold text-gray-900 dark:text-white md:text-4xl lg:text-5xl text-center">
        Read Any Ebook with <span class="text-blue-600 dark:text-blue-500">AI Dictionary</span> Support
      </h1>
      <div class="text-base text-center font-normal text-gray-500 lg:text-lg sm:px-0 lg:px-16 dark:text-gray-400">
        Instantly look up words, understand meanings in context, and save new vocabulary - all without leaving your book.
      </div>

      <div class="mt-8">
        <NuxtLink :to="appReaderUrl">
          <Button
            class="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-200">
            Try the Book Reader Now →
          </Button>
        </NuxtLink>
      </div>

      <img
        src="/epub-reader.webp"
        alt="Book Reader with AI Dictionary"
        class="w-full mt-8 shadow md:w-3/4 max-w-[1024px] md:mt-8 border rounded-lg bg-gray-100 dark:bg-gray-800 object-cover"
      />
    </div>

    <!-- Feature Introduction Sections -->
    <!-- 1. Integrated AI Dictionary -->
    <div class="flex flex-col md:flex-row items-center md:items-start justify-between max-w-screen-xl mx-auto mt-16 md:mt-32 py-10 px-4 md:px-12">
      <div class="md:w-1/2 md:pl-8 flex flex-col items-center md:items-start">
        <h2 class="mb-4 text-2xl font-bold text-gray-900 dark:text-white md:text-3xl text-center md:text-left">
          Integrated AI dictionary
        </h2>
        <p class="mb-6 text-base font-normal text-gray-500 dark:text-gray-400 lg:text-lg text-center md:text-left">
          No more switching between your book and a dictionary. Instantly access AI-powered definitions and contextual explanations right where you're reading.
        </p>

        <div class="space-y-4">
          <div class="flex items-center">
            <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            <p class="ml-2">Select any word or phrase to get a clear, context-aware explanation</p>
          </div>
          <div class="flex items-center">
            <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            <p class="ml-2">Maintain your reading flow - never lose your place or momentum</p>
          </div>
          <div class="flex items-center">
            <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            <p class="ml-2">Understand idioms, phrases, and nuanced meanings in real time</p>
          </div>
        </div>
      </div>

      <div class="mt-8 md:mt-0 md:w-1/2">
        <!-- Placeholder for Integrated AI Dictionary visual -->
        <video
          src="~/assets/epub-reader.mp4"
          class="flex items-center justify-center w-full bg-gray-200 dark:bg-gray-700 rounded-lg object-cover"
          controls
          autoplay
          muted
          loop
          playsinline
          aria-label="Demo of the Book Reader with AI Dictionary feature">
          <track
            kind="captions"
            src="/vtt/epub-reader.vtt"
            srclang="en"
            label="English">
        </video>
      </div>
    </div>

    <!-- 2. Save Words with Context -->
    <div class="flex flex-col md:flex-row-reverse items-center md:items-start justify-between max-w-screen-xl mx-auto mt-16 py-10 px-4 md:px-12 bg-gray-50 dark:bg-gray-800 rounded-lg">
      <div class="md:w-1/2 md:pl-8 flex flex-col items-center md:items-start">
        <h2 class="mb-4 text-2xl font-bold text-gray-900 dark:text-white md:text-3xl text-center md:text-left">
          Save new words - with their original context
        </h2>
        <p class="mb-6 text-base font-normal text-gray-500 dark:text-gray-400 lg:text-lg text-center md:text-left">
          Build a powerful vocabulary by saving words and phrases along with the exact sentence they appeared in.
        </p>

        <div class="space-y-4">
          <div class="flex items-center">
            <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            <p class="ml-2">One tap saves the word, its meaning, and the sentence from your book</p>
          </div>
          <div class="flex items-center">
            <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            <p class="ml-2">Review vocabulary in the context you first encountered it—making learning stick</p>
          </div>
          <div class="flex items-center">
            <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            <p class="ml-2">Organize saved words into personalized notebooks for targeted review</p>
          </div>
        </div>
      </div>

      <div class="mt-8 md:mt-0 md:w-1/2">
        <!-- Placeholder for Save Words with Context visual -->
        <img
          src="/epub-reader-save.png"
          alt="Save words with context demo"
          class="flex items-center justify-center w-full bg-gray-200 dark:bg-gray-700 rounded-lg object-cover"
        />
      </div>
    </div>

    <!-- 3. Read Anywhere, On Any Device -->
    <div class="flex flex-col items-center max-w-screen-xl mx-auto mt-16 mb-16 md:mb-28 py-10 px-4 md:px-12">
      <h2 class="mb-4 text-2xl font-bold text-gray-900 dark:text-white md:text-3xl text-center">
        Read anywhere - seamless on mobile, tablet, and desktop
      </h2>
      <p class="mb-6 text-base font-normal text-gray-500 dark:text-gray-400 lg:text-lg text-center">
        Enjoy a smooth, responsive reading experience wherever you are.
      </p>

      <div class="flex justify-center mt-8 w-full">
        <img
          src="/web-devices.svg"
          alt="Read on any device"
          class="w-full md:w-3/4 max-w-[768px] object-cover"
        />
      </div>
    </div>

    <!-- Benefits Section -->
    <div class="w-full bg-gradient-to-b from-blue-50 to-indigo-50 dark:from-blue-900 dark:to-indigo-900 py-16 overflow-hidden">
      <div class="max-w-screen-xl mx-auto px-4 md:px-12 relative">
        <!-- Decorative elements -->
        <div class="absolute top-0 left-0 w-32 h-32 bg-blue-100 dark:bg-blue-800 rounded-full opacity-30 blur-xl -translate-x-1/2 -translate-y-1/2"></div>
        <div class="absolute bottom-0 right-0 w-40 h-40 bg-indigo-100 dark:bg-indigo-800 rounded-full opacity-30 blur-xl translate-x-1/4 translate-y-1/4"></div>
        <div class="absolute top-1/3 right-1/4 w-16 h-16 bg-purple-100 dark:bg-purple-800 rounded-full opacity-20 blur-lg"></div>
        <div class="absolute bottom-1/3 left-1/4 w-24 h-24 bg-blue-200 dark:bg-blue-700 rounded-full opacity-20 blur-lg"></div>

        <div class="relative z-10 mb-12 text-center">
          <h2 class="text-3xl font-extrabold text-gray-900 dark:text-white md:text-4xl lg:text-5xl">
            Elevate your <span class="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-400 dark:to-indigo-400">English reading</span>
          </h2>
          <div class="w-24 h-1 bg-gradient-to-r from-blue-600 to-indigo-600 mx-auto mt-6"></div>
        </div>

        <!-- Benefits cards for mobile and timeline for desktop -->
        <div class="relative z-10">
          <!-- Center line (desktop only) -->
          <div class="absolute left-1/2 top-0 bottom-0 w-0.5 bg-gradient-to-b from-blue-400 to-indigo-500 dark:from-blue-500 dark:to-indigo-400 transform -translate-x-1/2 hidden md:block"></div>

          <!-- Benefit 1 -->
          <div class="flex flex-col md:flex-row items-center mb-4 md:mb-16 relative">
            <!-- Mobile card layout -->
            <div class="w-full md:hidden mb-6 bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
              <div class="flex items-center mb-4">
                <div class="p-3 bg-blue-50 dark:bg-blue-900 rounded-full mr-4">
                  <IconBook class="w-8 h-8 text-blue-600 dark:text-blue-400" :stroke-width="2" />
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white">Accelerate vocabulary acquisition</h3>
              </div>
              <p class="text-gray-600 dark:text-gray-300">Learn new words in their natural context, making them easier to remember and use correctly in your own communication.</p>
            </div>

            <!-- Desktop layout (left side) -->
            <div class="hidden md:block md:w-1/2 md:pr-12 md:text-right">
              <div class="transform transition-all duration-500 hover:-translate-y-1 hover:scale-105">
                <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-3 flex md:justify-end items-center">
                  <span>Accelerate vocabulary acquisition</span>
                  <div class="md:flex md:order-first md:mr-4 p-3 bg-blue-50 dark:bg-blue-900 rounded-full">
                    <IconBook class="w-8 h-8 text-blue-600 dark:text-blue-400" :stroke-width="2" />
                  </div>
                </h3>
                <p class="text-gray-600 dark:text-gray-300">Learn new words in their natural context, making them easier to remember and use correctly in your own communication.</p>
              </div>
            </div>

            <!-- Timeline node (desktop only) -->
            <div class="hidden md:flex absolute left-1/2 transform -translate-x-1/2 w-6 h-6 rounded-full bg-white dark:bg-gray-800 border-4 border-blue-500 dark:border-blue-400 z-10"></div>

            <div class="hidden md:block md:w-1/2 md:pl-12"></div>
          </div>

          <!-- Benefit 2 -->
          <div class="flex flex-col md:flex-row items-center mb-4 md:mb-16 relative">
            <!-- Mobile card layout -->
            <div class="w-full md:hidden mb-6 bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
              <div class="flex items-center mb-4">
                <div class="p-3 bg-indigo-50 dark:bg-indigo-900 rounded-full mr-4">
                  <IconBulb class="w-8 h-8 text-indigo-600 dark:text-indigo-400" :stroke-width="2" />
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white">Improved reading comprehension</h3>
              </div>
              <p class="text-gray-600 dark:text-gray-300">Instantly understand difficult words and phrases without disrupting your reading flow, enhancing overall comprehension.</p>
            </div>

            <!-- Desktop layout (right side) -->
            <div class="hidden md:block md:w-1/2 md:pr-12"></div>

            <!-- Timeline node (desktop only) -->
            <div class="hidden md:flex absolute left-1/2 transform -translate-x-1/2 w-6 h-6 rounded-full bg-white dark:bg-gray-800 border-4 border-indigo-500 dark:border-indigo-400 z-10"></div>

            <div class="hidden md:block md:w-1/2 md:pl-12">
              <div class="transform transition-all duration-500 hover:-translate-y-1 hover:scale-105">
                <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-3 flex items-center">
                  <div class="mr-4 p-3 bg-indigo-50 dark:bg-indigo-900 rounded-full">
                    <IconBulb class="w-8 h-8 text-indigo-600 dark:text-indigo-400" :stroke-width="2" />
                  </div>
                  <span>Improved reading comprehension</span>
                </h3>
                <p class="text-gray-600 dark:text-gray-300">Instantly understand difficult words and phrases without disrupting your reading flow, enhancing overall comprehension.</p>
              </div>
            </div>
          </div>

          <!-- Benefit 3 -->
          <div class="flex flex-col md:flex-row items-center mb-4 md:mb-16 relative">
            <!-- Mobile card layout -->
            <div class="w-full md:hidden mb-6 bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
              <div class="flex items-center mb-4">
                <div class="p-3 bg-blue-50 dark:bg-blue-900 rounded-full mr-4">
                  <IconBookmark class="w-8 h-8 text-blue-600 dark:text-blue-400" :stroke-width="2" />
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white">Contextual saving and review</h3>
              </div>
              <p class="text-gray-600 dark:text-gray-300">Save words with their original context for more effective review and better long-term retention of vocabulary.</p>
            </div>

            <!-- Desktop layout (left side) -->
            <div class="hidden md:block md:w-1/2 md:pr-12 md:text-right">
              <div class="transform transition-all duration-500 hover:-translate-y-1 hover:scale-105">
                <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-3 flex md:justify-end items-center">
                  <span>Contextual saving and review</span>
                  <div class="md:flex md:order-first md:mr-4 p-3 bg-blue-50 dark:bg-blue-900 rounded-full">
                    <IconBookmark class="w-8 h-8 text-blue-600 dark:text-blue-400" :stroke-width="2" />
                  </div>
                </h3>
                <p class="text-gray-600 dark:text-gray-300">Save words with their original context for more effective review and better long-term retention of vocabulary.</p>
              </div>
            </div>

            <!-- Timeline node (desktop only) -->
            <div class="hidden md:flex absolute left-1/2 transform -translate-x-1/2 w-6 h-6 rounded-full bg-white dark:bg-gray-800 border-4 border-blue-500 dark:border-blue-400 z-10"></div>

            <div class="hidden md:block md:w-1/2 md:pl-12"></div>
          </div>

          <!-- Benefit 4 -->
          <div class="flex flex-col md:flex-row items-center relative">
            <!-- Mobile card layout -->
            <div class="w-full md:hidden mb-6 bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
              <div class="flex items-center mb-4">
                <div class="p-3 bg-indigo-50 dark:bg-indigo-900 rounded-full mr-4">
                  <IconStar class="w-8 h-8 text-indigo-600 dark:text-indigo-400" :stroke-width="2" />
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white">Confident reading experience</h3>
              </div>
              <p class="text-gray-600 dark:text-gray-300">Enjoy English books with confidence, knowing you can understand every word and expression you encounter.</p>
            </div>

            <!-- Desktop layout (right side) -->
            <div class="hidden md:block md:w-1/2 md:pr-12"></div>

            <!-- Timeline node (desktop only) -->
            <div class="hidden md:flex absolute left-1/2 transform -translate-x-1/2 w-6 h-6 rounded-full bg-white dark:bg-gray-800 border-4 border-indigo-500 dark:border-indigo-400 z-10"></div>

            <div class="hidden md:block md:w-1/2 md:pl-12">
              <div class="transform transition-all duration-500 hover:-translate-y-1 hover:scale-105">
                <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-3 flex items-center">
                  <div class="mr-4 p-3 bg-indigo-50 dark:bg-indigo-900 rounded-full">
                    <IconStar class="w-8 h-8 text-indigo-600 dark:text-indigo-400" :stroke-width="2" />
                  </div>
                  <span>Confident reading experience</span>
                </h3>
                <p class="text-gray-600 dark:text-gray-300">Enjoy English books with confidence, knowing you can understand every word and expression you encounter.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Testimonials Section -->
    <div class="max-w-screen-xl mx-auto px-4 md:px-12 py-16 md:py-24">
      <div class="text-center mb-12">
        <h2 class="text-3xl font-extrabold text-gray-900 dark:text-white md:text-4xl">
          What Our Readers Say
        </h2>
        <div class="w-24 h-1 bg-gradient-to-r from-blue-600 to-indigo-600 mx-auto mt-4"></div>
        <p class="mt-4 text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
          Discover how Pickvocab's Book Reader is transforming the way people learn English
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- Testimonial 1 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 transform transition duration-300 hover:-translate-y-2">
          <div class="flex items-center mb-4">
            <img src="/mai-tran.jpg" alt="Mai Tran" class="h-12 w-12 rounded-full object-cover bg-gray-200 dark:bg-gray-700" />
            <div class="ml-4">
              <h3 class="font-semibold text-lg text-gray-900 dark:text-white">Mai Tran</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">MBA candidate - UCLA</p>
            </div>
          </div>
          <div class="relative">
            <svg class="absolute top-0 left-0 w-8 h-8 text-green-200 dark:text-green-800 transform -translate-x-4 -translate-y-4" fill="currentColor" viewBox="0 0 32 32" aria-hidden="true">
              <path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z" />
            </svg>
            <p class="relative text-gray-700 dark:text-gray-300 italic">
              The integrated dictionary is a game-changer. Unlike other ebook readers with basic dictionaries, Pickvocab lets me look up any selected phrases and idioms that traditional dictionaries can't handle. I can read challenging books without constantly switching to a translator app. My vocabulary has grown significantly in just two months.
            </p>
          </div>
        </div>

        <!-- Testimonial 2 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 transform transition duration-300 hover:-translate-y-2">
          <div class="flex items-center mb-4">
            <img src="/sarah-kim.jpg" alt="Sarah Kim" class="h-12 w-12 rounded-full object-cover bg-gray-200 dark:bg-gray-700" />
            <div class="ml-4">
              <h3 class="font-semibold text-lg text-gray-900 dark:text-white">Sarah Kim</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">Undergraduate - Seoul National University</p>
            </div>
          </div>
          <div class="relative">
            <svg class="absolute top-0 left-0 w-8 h-8 text-green-200 dark:text-green-800 transform -translate-x-4 -translate-y-4" fill="currentColor" viewBox="0 0 32 32" aria-hidden="true">
              <path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z" />
            </svg>
            <p class="relative text-gray-700 dark:text-gray-300 italic">
              Being able to save words with their original context has revolutionized how I study. When I review them later, I can instantly recall how they were used, which makes learning so much more effective.
            </p>
          </div>
        </div>

        <!-- Testimonial 3 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 transform transition duration-300 hover:-translate-y-2">
          <div class="flex items-center mb-4">
            <img src="/rafael.jpg" alt="Rafael Martinez" class="h-12 w-12 rounded-full object-cover bg-gray-200 dark:bg-gray-700" />
            <div class="ml-4">
              <h3 class="font-semibold text-lg text-gray-900 dark:text-white">Rafael Martinez</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">Project Manager - PagSeguro Digital Ltd</p>
            </div>
          </div>
          <div class="relative">
            <svg class="absolute top-0 left-0 w-8 h-8 text-green-200 dark:text-green-800 transform -translate-x-4 -translate-y-4" fill="currentColor" viewBox="0 0 32 32" aria-hidden="true">
              <path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z" />
            </svg>
            <p class="relative text-gray-700 dark:text-gray-300 italic">
              I read on my phone during commutes and on my laptop at home. The seamless experience across devices has made it easy to incorporate reading into my daily routine.
            </p>
          </div>
        </div>
      </div>

      <!-- Call to Action -->
      <div class="mt-12 text-center">
        <NuxtLink :to="appReaderUrl">
          <Button
            class="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-200">
            Join Thousands of Happy Readers →
          </Button>
        </NuxtLink>
      </div>
    </div>

    <HomeFooter class=""></HomeFooter>
  </div>
</template>

<style scoped></style>

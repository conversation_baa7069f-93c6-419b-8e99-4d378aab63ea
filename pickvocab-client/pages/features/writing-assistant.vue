<script setup lang="ts">
import {
  FwbNavbar,
  FwbNavbarCollapse,
} from "flowbite-vue";
import HomeFooter from "~/components/home/<USER>";
import HomeNavbar from '~/components/home/<USER>';
// @ts-ignore
import IconEdit from '@tabler/icons-vue/dist/esm/icons/IconEdit.mjs';
// @ts-ignore
import IconSparkles from '@tabler/icons-vue/dist/esm/icons/IconSparkles.mjs';
// @ts-ignore
import IconBook from '@tabler/icons-vue/dist/esm/icons/IconBook.mjs';
// @ts-ignore
import IconVocabulary from '@tabler/icons-vue/dist/esm/icons/IconVocabulary.mjs';
// @ts-ignore
import IconAdjustments from '@tabler/icons-vue/dist/esm/icons/IconAdjustments.mjs';
// @ts-ignore
import IconTrendingUp from '@tabler/icons-vue/dist/esm/icons/IconTrendingUp.mjs';
// @ts-ignore
import IconCheck from '@tabler/icons-vue/dist/esm/icons/IconCheck.mjs';
// @ts-ignore
import IconArrowRight from '@tabler/icons-vue/dist/esm/icons/IconArrowRight.mjs';


useSeoMeta({
  title: "AI Writing Assistant | Pickvocab",
  description: "Enhance English writing with real-time AI feedback. Fix grammar, improve style, and use vocabulary in context. Perfect for essays, emails, and professional writing.",
  keywords: "AI writing assistant, English grammar checker, vocabulary enhancer, academic writing help, professional email writing"
});
</script>

<template>
  <div>
    <HomeNavbar />

    <!-- Hero Section -->
    <div class="flex flex-col items-center mt-16 md:mt-24 py-10 px-4 md:px-8">
      <h1 class="mb-4 text-3xl font-extrabold text-gray-900 dark:text-white md:text-4xl lg:text-5xl text-center">
        Transform Your Writing with <span class="text-blue-600 dark:text-blue-500">AI Assistant</span>
      </h1>
      <div class="text-base text-center font-normal text-gray-500 lg:text-lg sm:px-0 lg:px-16 dark:text-gray-400">
        Write confidently, master your vocabulary, and track your progress - all in one powerful tool
      </div>

      <div class="mt-8 flex flex-col sm:flex-row sm:space-x-4 space-y-4 sm:space-y-0 items-center justify-center">
        <NuxtLink to="https://chromewebstore.google.com/detail/pickvocab-ai-powered-dict/nfhhjfaahjkjdjbkpacapdblonogknag" target="_blank" rel="noopener noreferrer">
          <Button
            class="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-200">
            Install Extension →
          </Button>
        </NuxtLink>
        <NuxtLink to="/app/write">
          <Button
            variant="outline"
            class="w-full sm:w-auto bg-white hover:bg-gray-100 text-gray-800 dark:text-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 font-medium px-6 py-3 rounded-lg border border-gray-200 dark:border-gray-600 transition-all duration-200">
            Try on Web
          </Button>
        </NuxtLink>
      </div>

            <video
              src="~/assets/writing-assistant.mp4"
              class="w-full mt-8 max-w-[1024px] rounded-lg"
              controls
              autoplay
              muted
              loop
              playsinline
              fetchpriority="high"
              aria-label="Demo of Pickvocab's Writing Assistant in action">
              <track
                kind="captions"
                src="/vtt/writing-assistant.vtt"
                srclang="en"
                label="English">
            </video>
          </div>

    <!-- 1. Rewrite with Confidence -->
    <div class="flex flex-col md:flex-row items-center md:items-start justify-between max-w-screen-xl mx-auto mt-16 md:mt-32 py-10 px-4 md:px-12">
      <div class="md:w-1/2 md:pl-8 flex flex-col items-center md:items-start">
        <div class="flex items-center justify-center md:justify-start mb-4">
          <div class="p-3 bg-blue-50 dark:bg-blue-900 rounded-full">
            <IconEdit class="w-8 h-8 text-blue-600 dark:text-blue-400" :stroke-width="1.5" />
          </div>
        </div>
        <h2 class="mb-4 text-2xl font-bold text-gray-900 dark:text-white md:text-3xl text-center md:text-left">
          Your personal AI editor
        </h2>

        <div class="space-y-4">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            </div>
            <p class="ml-2">Improve fluency, clarity, and grammar instantly</p>
          </div>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            </div>
            <p class="ml-2">Refine phrasing to make your writing shine</p>
          </div>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            </div>
            <p class="ml-2">Receive real-time feedback with easy-to-understand explanations</p>
          </div>
        </div>
      </div>

      <div class="mt-8 md:mt-0 md:w-1/2">
        <!-- Placeholder for Rewrite Visual -->
        <div class="flex items-center justify-center w-full rounded-lg">
          <img
            src="/revise-writing.webp"
            alt="Writing Assistant"
            title="Writing Assistant"
            class="w-full object-cover" />
        </div>
      </div>
    </div>

    <!-- 2. Bring Your Vocabulary to Life -->
    <div class="flex flex-col md:flex-row-reverse items-center md:items-start justify-between max-w-screen-xl mx-auto mt-16 py-10 px-4 md:px-12 bg-gray-50 dark:bg-gray-800 rounded-lg">
      <div class="md:w-1/2 md:pl-8 flex flex-col items-center md:items-start">
        <div class="flex items-center justify-center md:justify-start mb-4">
          <div class="p-3 bg-indigo-50 dark:bg-indigo-900 rounded-full">
            <IconVocabulary class="w-8 h-8 text-indigo-600 dark:text-indigo-400" :stroke-width="1.5" />
          </div>
        </div>
        <h2 class="mb-4 text-2xl font-bold text-gray-900 dark:text-white md:text-3xl text-center md:text-left">
          Use the words you've learned
        </h2>

        <div class="space-y-4">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            </div>
            <p class="ml-2">Seamlessly incorporate words from your vocabulary notebooks</p>
          </div>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            </div>
            <p class="ml-2">Get context-appropriate usage suggestions for saved words</p>
          </div>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            </div>
            <p class="ml-2">Reinforce vocabulary retention by using learned words in real writing</p>
          </div>
        </div>
      </div>

      <div class="mt-8 md:mt-0 md:w-1/2">
        <!-- Placeholder for Vocabulary Integration Visual -->
        <div class="flex items-center justify-center w-full rounded-lg border border-gray-200 p-2 bg-white">
          <img
            src="/use-vocab.webp"
            alt="Animation showing vocabulary suggestions"
            title="Animation showing vocabulary suggestions"
            class="w-full object-cover" />
        </div>
      </div>
    </div>

    <!-- 3. Write for Any Occasion -->
    <div class="flex flex-col md:flex-row items-center md:items-start justify-between max-w-screen-xl mx-auto mt-16 py-10 px-4 md:px-12">
      <div class="md:w-1/2 md:pl-8 flex flex-col items-center md:items-start">
        <div class="flex items-center justify-center md:justify-start mb-4">
          <div class="p-3 bg-blue-50 dark:bg-blue-900 rounded-full">
            <IconAdjustments class="w-8 h-8 text-blue-600 dark:text-blue-400" :stroke-width="1.5" />
          </div>
        </div>
        <h2 class="mb-4 text-2xl font-bold text-gray-900 dark:text-white md:text-3xl text-center md:text-left">
          Adapt your writing style
        </h2>

        <div class="space-y-4">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            </div>
            <p class="ml-2">Choose from academic, professional, casual, or custom tones</p>
          </div>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            </div>
            <p class="ml-2">Switch effortlessly between styles to match your writing needs</p>
          </div>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            </div>
            <p class="ml-2">Maintain a consistent voice throughout your text</p>
          </div>
        </div>
      </div>

      <div class="mt-8 md:mt-0 md:w-1/2">
        <!-- Placeholder for Tone Selector Visual -->
        <div class="flex items-center justify-center w-full rounded-lg border border-gray-200 p-4">
          <img
            src="/tones.webp"
            alt="Custom tones"
            title="Custom tones"
            class="w-full object-cover" />
        </div>
      </div>
    </div>

    <!-- 4. See Your Progress -->
    <div class="flex flex-col md:flex-row-reverse items-center md:items-start justify-between max-w-screen-xl mx-auto mt-16 py-16 px-4 md:px-12 bg-gray-50 dark:bg-gray-800 rounded-lg">
      <div class="md:w-1/2 md:pl-8 flex flex-col items-center md:items-start">
        <div class="flex items-center justify-center md:justify-start mb-4">
          <div class="p-3 bg-indigo-50 dark:bg-indigo-900 rounded-full">
            <IconTrendingUp class="w-8 h-8 text-indigo-600 dark:text-indigo-400" :stroke-width="1.5" />
          </div>
        </div>
        <h2 class="mb-4 text-2xl font-bold text-gray-900 dark:text-white md:text-3xl text-center md:text-left">
          Track your growth
        </h2>

        <div class="space-y-4">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            </div>
            <p class="ml-2">View and compare all document revisions</p>
          </div>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            </div>
            <p class="ml-2">Track how often you use saved vocabulary words</p>
          </div>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <IconCheck class="w-6 h-6 text-green-500 dark:text-white" :stroke-width="1.5" />
            </div>
            <p class="ml-2">Identify under-utilized vocabulary for focused practice</p>
          </div>
        </div>
      </div>

      <div class="mt-8 md:mt-0 md:w-1/2">
        <!-- Progress Dashboard Visual -->
        <div class="flex items-center justify-center w-full rounded-lg">
          <img src="/stepping-up.svg" alt="Progress dashboard showing statistics" class="max-w-[300px]" />
        </div>
      </div>
    </div>

    <!-- Integration Diagram Section -->
    <div class="py-16 md:py-24 bg-white dark:bg-gray-900">
      <div class="max-w-screen-xl mx-auto px-4 md:px-12">
        <div class="text-center mb-12">
        <h2 class="text-3xl font-extrabold text-gray-900 dark:text-white md:text-4xl">
          Learn → Write → Remember
        </h2>
        <div class="w-24 h-1 bg-gradient-to-r from-blue-600 to-indigo-600 mx-auto mt-4"></div>
        <p class="mt-4 text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
          Turn saved words into active vocabulary you can actually use
        </p>
      </div>

      <!-- Visual workflow -->
      <div class="flex flex-col md:flex-row items-center justify-center gap-4 md:gap-8 mb-8">
        <!-- Learning Step -->
        <div class="flex flex-col items-center text-center p-6 bg-white dark:bg-slate-800 rounded-xl border border-slate-200 dark:border-slate-700 w-full md:w-64 min-h-[180px] md:min-h-[220px] shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300 ease-in-out">
          <div class="mb-3 p-3 bg-white dark:bg-slate-700 rounded-full border-2 border-blue-500 dark:border-blue-400 shadow-md">
            <IconBook class="w-8 h-8 text-blue-600 dark:text-blue-400" :stroke-width="2" />
          </div>
          <h3 class="font-bold text-lg mb-3">1. Learn Words</h3>
          <p class="text-sm text-gray-600 dark:text-gray-400 px-2 leading-relaxed">
            Capture new words from books or web content, preserving their original context and examples.
          </p>
        </div>

        <IconArrowRight class="w-8 h-8 text-blue-500 dark:text-blue-400 hidden md:block" :stroke-width="1.5" />

        <!-- Practice Step -->
        <div class="flex flex-col items-center text-center p-6 bg-white dark:bg-slate-800 rounded-xl border border-slate-200 dark:border-slate-700 w-full md:w-64 min-h-[180px] md:min-h-[220px] shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300 ease-in-out">
          <div class="mb-3 p-3 bg-white dark:bg-slate-700 rounded-full border-2 border-purple-500 dark:border-purple-400 shadow-md">
            <IconEdit class="w-8 h-8 text-purple-600 dark:text-purple-400" :stroke-width="2" />
          </div>
          <h3 class="font-bold text-lg mb-3">2. Practice Writing</h3>
          <p class="text-sm text-gray-600 dark:text-gray-400 px-2 leading-relaxed">
            Apply your saved vocabulary in writing with AI-powered suggestions and real-time feedback.
          </p>
        </div>

        <IconArrowRight class="w-8 h-8 text-blue-500 dark:text-blue-400 hidden md:block" :stroke-width="1.5" />

        <!-- Mastery Step -->
        <div class="flex flex-col items-center text-center p-6 bg-white dark:bg-slate-800 rounded-xl border border-slate-200 dark:border-slate-700 w-full md:w-64 min-h-[180px] md:min-h-[220px] shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300 ease-in-out">
          <div class="mb-3 p-3 bg-white dark:bg-slate-700 rounded-full border-2 border-green-500 dark:border-green-400 shadow-md">
            <IconTrendingUp class="w-8 h-8 text-green-600 dark:text-green-400" :stroke-width="2" />
          </div>
          <h3 class="font-bold text-lg mb-3">3. Remember & Grow</h3>
          <p class="text-sm text-gray-600 dark:text-gray-400 px-2 leading-relaxed">
            Reinforce learning by tracking vocabulary usage and identifying areas for continuous improvement.
          </p>
        </div>
        </div>
      </div>


      <!-- Final CTA -->
    </div>
    <div class="mt-0 py-16 md:py-20 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-slate-800 dark:via-gray-800/70 dark:to-slate-900">
      <div class="max-w-screen-xl mx-auto px-4 md:px-12 text-center">
        <blockquote class="mb-8 text-xl md:text-2xl text-gray-700 dark:text-gray-300 italic max-w-3xl mx-auto leading-relaxed">
          "You can always edit a bad page. You can't edit a blank page."
          <cite class="block mt-3 text-base text-gray-500 dark:text-gray-400 not-italic">- Jodi Picoult</cite>
        </blockquote>
        <NuxtLink to="/app/write">
          <Button class="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-200">
            Start Writing →
          </Button>
        </NuxtLink>
      </div>
    </div>
    <HomeFooter />
  </div>
</template>

<style scoped></style>

import * as locale from 'locale-codes';

export const languages = [
  { value: 'English', name: 'English - English' },
  { value: 'Vietnamese', name: 'English - Vietnamese' },
  { value: 'Spanish', name: 'English - Spanish' },
  { value: 'Chinese (Simplified)', name: 'English - Chinese (Simplified)' },
  { value: 'Hindi', name: 'English - Hindi' },
  { value: 'Korean', name: 'English - Korean' },
  { value: 'Japanese', name: 'English - Japanese' },
  { value: 'German', name: 'English - German' },
  { value: 'French', name: 'English - French' },
  { value: 'Russian', name: 'English - Russian' },
  { value: 'Portuguese', name: 'English - Portuguese' },
  { value: 'Indonesian', name: 'English - Indonesian' },
  { value: 'Turkish', name: 'English - Turkish' },
  { value: 'Italian', name: 'English - Italian' },
  { value: 'Arabic', name: 'English - Arabic' },
  { value: 'Bangla', name: 'English - Bangla' },
  { value: 'Burmese', name: 'English - Burmese' },
]

export function generateAllHrefLangLinks (word: string, prefixUrl: string) {
  return languages.map(lang => {
    const { tag } = locale.getByName(lang.value);
    return {
      hreflang: tag,
      href: `${prefixUrl}/dictionary/${tag}/${word}`
    }
  });
}
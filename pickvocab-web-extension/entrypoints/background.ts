import { getUser, registerAnnonymousUser } from '@/api/auth';
import { LLMModelsApi } from '@/api/llm';
import { RemoteWordInContextApi } from '@/api/wordInContext';
import { onMessage, sendMessage } from 'webext-bridge/background';
import { onLaunchWebFlow } from './auth/google';
import { RemoteGenericCardsApi } from '@/api/genericCard';
import { RemoteDecksApi } from '@/api/deck';
import { RemoteCustomTonesApi, CustomTonePayload } from '@/api/customTones';
import * as axiosLib from 'axios';
import { handleGenerateRevision } from './background/revisionHandler';
import { handleSaveHistory, handleUpdateHistory } from './background/historyHandler';
import { handleSimilaritySearch } from './background/similaritySearchHandler';
import { handleHighlightVocabulary } from './background/highlightHandler';
import { handleVocabularyCardsCheck } from './background/vocabularyCardsHandler';
import { SaveHistoryPayload } from '@/components/write/api';

const CONTEXT_MENU_ID = 'contextual-lookup';
const CONTEXT_MENU_ID_FIX_WRITING = 'fix-writing';

async function getToken() {
  const persistedStore = await storage.getItem('local:auth') as any;

  if (!persistedStore) {
    const { key } = await registerAnnonymousUser();
    const annonymousUser = await getUser(key);
    const annonymousToken = key;
    storage.setItem('local:auth', {
      annonymousUser,
      annonymousToken,
      version: Number(import.meta.env.WXT_AUTH_STORE_VERSION || 0),
    });
    return annonymousToken;
  }

  return persistedStore.token || persistedStore.annonymousToken;
}

export default defineBackground(() => { // Removed async here as WXT expects a sync function for defineBackground
  let token: string | null = null; // Declare token in the higher scope, accessible by googleSignIn

  // Handle installation and startup events IMMEDIATELY
  chrome.runtime.onInstalled.addListener(async (details) => {
    console.log("Pickvocab Extension: chrome.runtime.onInstalled event fired. Reason:", details.reason);

    if (details.reason === chrome.runtime.OnInstalledReason.INSTALL) {
      console.log("Pickvocab Extension: Installation reason is 'install'. Checking storage flag.");

      const result = await chrome.storage.local.get('pickvocab_onboarding_seen');
      console.log("Pickvocab Extension: Storage check for 'pickvocab_onboarding_seen':", result.pickvocab_onboarding_seen);

      if (!result.pickvocab_onboarding_seen) {
        console.log("Pickvocab Extension: Onboarding flag not set. Attempting to open onboarding page.");
        try {
          const onboardingUrl = chrome.runtime.getURL("onboarding.html");
          console.log("Pickvocab Extension: Onboarding page URL:", onboardingUrl);
          chrome.tabs.create({ url: onboardingUrl });
          console.log("Pickvocab Extension: chrome.tabs.create called.");
        } catch (error) {
          console.error("Pickvocab Extension: Error creating onboarding tab:", error);
        }
      } else {
        console.log("Pickvocab Extension: Onboarding flag already set. Not opening onboarding page.");
      }
    } else if (details.reason === chrome.runtime.OnInstalledReason.UPDATE) {
      console.log("Pickvocab Extension: Installation reason is 'update'. Previous version:", details.previousVersion);
      // TODO: Handle 'update' reason if needed in the future (e.g., show update notes)
    } else {
      console.log("Pickvocab Extension: Installation reason is something else:", details.reason);
    }
  });

  chrome.runtime.onStartup.addListener(async () => {
    console.log("Pickvocab Extension: chrome.runtime.onStartup event fired.");
    const result = await chrome.storage.local.get('pickvocab_onboarding_seen');
    console.log("Pickvocab Extension: Storage check for 'pickvocab_onboarding_seen' on startup:", result.pickvocab_onboarding_seen);

    if (!result.pickvocab_onboarding_seen) {
      console.log("Pickvocab Extension: Onboarding flag not set on startup. Opening onboarding page.");
      try {
        const onboardingUrl = chrome.runtime.getURL("onboarding.html");
        console.log("Pickvocab Extension: Onboarding page URL:", onboardingUrl);
        chrome.tabs.create({ url: onboardingUrl });
        console.log("Pickvocab Extension: chrome.tabs.create called on startup.");
      } catch (error) {
        console.error("Pickvocab Extension: Error creating onboarding tab on startup:", error);
      }
    } else {
      console.log("Pickvocab Extension: Onboarding flag already set on startup.");
    }
  });

  // Asynchronous initializations can happen after listeners are set up
  (async () => {
    // Set action tooltip based on operating system
    const isMac = /Mac|iPhone|iPad|iPod/i.test(navigator.userAgent);
    const shortcut = isMac ? 'Cmd+Shift+K' : 'Ctrl+Shift+K';
    chrome.action.setTitle({ title: `Pickvocab (${shortcut})` });

    token = await getToken(); // Assign to the higher-scoped token
    let axios = getAxiosInstance();
    if (token) { // Ensure token is not null before using it
      axios.defaults.headers.common['Authorization'] = `Token ${token}`;
    }
    console.log('OK'); // Original 'OK' log

    // Existing onMessage handlers and other async logic follow here
    // ...
  })();


  // onMessage('registerAnnonymousUser', async () => {
  //   return registerAnnonymousUser();
  // });

  // llm apis
  onMessage('llm:list', async (message) => {
    const api = new LLMModelsApi();
    return api.list();
  });

  // wordInContext apis
  onMessage('wordInContext:create', async (message) => {
    const { base } = message.data as any;
    const api = new RemoteWordInContextApi();
    return api.create(base);
  });

  onMessage('wordInContext:put', async (message) => {
    const { word } = message.data as any;

    const api = new RemoteWordInContextApi();
    return api.put(word);
  });

  onMessage('definitionCard:create', async (message) => {
    const { card } = message.data as any;
    const api = new RemoteGenericCardsApi();
    return api.createDefinitionCard(card);
  });

  onMessage('contextCard:create', async (message) => {
    const { card } = message.data as any;
    const api = new RemoteGenericCardsApi();
    return api.createContextCard(card);
  });

  onMessage('deck:addCard', async (message) => {
    const { deckId, cardId } = message.data as any;
    const api = new RemoteDecksApi();
    return api.addCard(deckId, cardId);
  });

  onMessage('deck:search', async (message) => {
    const { text } = message.data as any;
    const api = new RemoteDecksApi();
    return api.search(text);
  });

  onMessage('getUser', async (message) => {
    const { token } = message.data as any;
    return getUser(token);
  });

  onMessage('openOptionsPage', () => {
    browser.runtime.openOptionsPage();
  });

  onMessage('googleSignIn', async () => {
    const key = await onLaunchWebFlow();
    const axios = getAxiosInstance();
    axios.defaults.headers.common['Authorization'] = `Token ${key}`;
    const user = await getUser(key);
    const persistedStore = await storage.getItem('local:auth') as any;
    await storage.setItem('local:auth', {
      ...persistedStore,
      user,
      token: key,
    });
    token = key;
    axios.defaults.headers.common['Authorization'] = `Token ${token}`;
  });

  onMessage('openTab', async (message) => {
    const { url } = message.data as any;
    browser.tabs.create({
      url,
    });
  });

  browser.contextMenus.create({
    id: CONTEXT_MENU_ID,
    title: 'Lookup "%s" in this context',
    contexts: ['selection'],
  });

  browser.contextMenus.create({
    id: CONTEXT_MENU_ID_FIX_WRITING,
    title: 'Fix writing',
    contexts: ['selection'],
  });

  browser.contextMenus.onClicked.addListener((info, tab) => {
    if (info.menuItemId === CONTEXT_MENU_ID && info.selectionText && tab?.id) {
      sendMessage('LOOKUP_WORD', { word: info.selectionText }, `content-script@${tab.id}`);
    } else if (info.menuItemId === CONTEXT_MENU_ID_FIX_WRITING && info.selectionText && tab?.id) {
      sendMessage('TRIGGER_FIX_WRITING_POPUP', {}, `content-script@${tab.id}`);
    }
  });

  // Writing Assistant event names now use lowerCamelCase for consistency with other events.
  onMessage('writingAssistantFetchCustomTones', async (message) => {
    const api = new RemoteCustomTonesApi();
    try {
      const tones = await api.list();
      return { success: true, tones };
    } catch (error: any) {
      if (axiosLib.isAxiosError(error)) {
        if (error.response) {
          if (error.response.status === 401 || error.response.status === 403) {
            return { error: true, type: 'auth', status: error.response.status };
          }
          if (error.response.status === 400) {
            return { error: true, type: 'validation', detail: error.response.data };
          }
          return { error: true, type: 'api', status: error.response.status, detail: error.response.data };
        } else {
          return { error: true, type: 'network', message: error.message };
        }
      }
      return { error: true, type: 'unknown', message: String(error) };
    }
  });
  onMessage('writingAssistantCreateCustomTone', async (message) => {
    const api = new RemoteCustomTonesApi();
    try {
      const tone = await api.create((message.data as unknown) as CustomTonePayload);
      return { success: true, tone };
    } catch (error: any) {
      if (axiosLib.isAxiosError(error)) {
        if (error.response) {
          if (error.response.status === 401 || error.response.status === 403) {
            return { error: true, type: 'auth', status: error.response.status };
          }
          if (error.response.status === 400) {
            return { error: true, type: 'validation', detail: error.response.data };
          }
          return { error: true, type: 'api', status: error.response.status, detail: error.response.data };
        } else {
          return { error: true, type: 'network', message: error.message };
        }
      }
      return { error: true, type: 'unknown', message: String(error) };
    }
  });
  onMessage('writingAssistantUpdateCustomTone', async (message) => {
    const api = new RemoteCustomTonesApi();
    try {
      const { id, ...payload } = message.data as { id: number } & Partial<CustomTonePayload>;
      const tone = await api.update(id, payload);
      return { success: true, tone };
    } catch (error: any) {
      if (axiosLib.isAxiosError(error)) {
        if (error.response) {
          if (error.response.status === 401 || error.response.status === 403) {
            return { error: true, type: 'auth', status: error.response.status };
          }
          if (error.response.status === 404) {
            return { error: true, type: 'not_found', status: 404 };
          }
          return { error: true, type: 'api', status: error.response.status, detail: error.response.data };
        } else {
          return { error: true, type: 'network', message: error.message };
        }
      }
      return { error: true, type: 'unknown', message: String(error) };
    }
  });
  onMessage('writingAssistantDeleteCustomTone', async (message) => {
    const api = new RemoteCustomTonesApi();
    try {
      const { id } = message.data as { id: number };
      await api.delete(id);
      return { success: true };
    } catch (error: any) {
      if (axiosLib.isAxiosError(error)) {
        if (error.response) {
          if (error.response.status === 401 || error.response.status === 403) {
            return { error: true, type: 'auth', status: error.response.status };
          }
          if (error.response.status === 404) {
            return { error: true, type: 'not_found', status: 404 };
          }
          return { error: true, type: 'api', status: error.response.status, detail: error.response.data };
        } else {
          return { error: true, type: 'network', message: error.message };
        }
      }
      return { error: true, type: 'unknown', message: String(error) };
    }
  });
  onMessage('writingAssistantGenerateRevision', async (message) => {
    if (!message.data || typeof message.data !== 'object' || typeof (message.data as any).prompt !== 'string') {
      return { error: true, type: 'invalid_payload', message: 'Missing or invalid prompt in message data' };
    }
    
    const { prompt, indexToIdMap } = message.data as { 
      prompt: string;
      indexToIdMap?: Record<string, string>;
    };
    
    return await handleGenerateRevision({ prompt, indexToIdMap });
  });
  onMessage('writingAssistantSaveHistory', async (message) => {
    return await handleSaveHistory(message.data as unknown as SaveHistoryPayload);
  });
  onMessage('writingAssistantSimilaritySearch', async (message) => {
    return await handleSimilaritySearch(message.data);
  });
  onMessage('writingAssistantHighlightVocabulary', async (message) => {
    if (!message.data || typeof message.data !== 'object') {
      return { error: true, type: 'invalid_payload', message: 'Missing or invalid data in message' };
    }
    return await handleHighlightVocabulary(message.data as any);
  });
  onMessage('writingAssistantUpdateHistory', async (message) => {
    if (!message.data || typeof message.data !== 'object' || !('historyId' in message.data) || !('payload' in message.data)) {
      return { error: true, type: 'invalid_payload', message: 'Missing or invalid historyId/payload in message data' };
    }
    
    return await handleUpdateHistory(message.data as any);
  });
  
  // Updated handler for checking vocabulary cards
  onMessage('writingAssistantCheckVocabularyCards', async () => {
    return await handleVocabularyCardsCheck();
  });
});

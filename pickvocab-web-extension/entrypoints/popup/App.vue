<script lang="ts" setup>
const openOnboarding = () => {
  chrome.tabs.create({ url: chrome.runtime.getURL("onboarding.html") });
};
</script>

<template>
  <div id="scrollable-container" class="w-[600px] min-h-[420px] overflow-auto">
    <RouterView />

    <!-- Link to open onboarding page -->
    <div class="p-2 text-center">
      <a href="#" @click.prevent="openOnboarding" class="text-blue-500 hover:underline">View Onboarding</a>
    </div>
  </div>
</template>

<style scoped>
</style>

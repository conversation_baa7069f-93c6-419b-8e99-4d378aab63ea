<script lang="ts" setup>
import { ref } from 'vue';
import WelcomeStep from './components/WelcomeStep.vue';
import AiDictionaryStep from './components/AiDictionaryStep.vue';
import ContextualMeaningStep from './components/ContextualMeaningStep.vue';
import SaveWordsStep from './components/SaveWordsStep.vue';
import WritingAssistantStep from './components/WritingAssistantStep.vue';
import GetStartedStep from './components/GetStartedStep.vue';
import OnboardingProgress from './components/OnboardingProgress.vue';

const currentStep = ref(1);
const totalSteps = 6;

const nextStep = () => {
  if (currentStep.value < totalSteps) {
    currentStep.value++;
  }
};

const prevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--;
  }
};

const finishOnboarding = async () => {
  // Set flag in chrome.storage.local
  await chrome.storage.local.set({ pickvocab_onboarding_seen: true });
  console.log("Onboarding Finished! Flag set in storage.");

  // Close the current tab
  window.close();
};

// TODO: Import and use other step components
</script>

<template>
  <div class="onboarding-container flex flex-col h-full">
    <!-- Progress Indicator -->
    <OnboardingProgress :currentStep="currentStep" :totalSteps="totalSteps" class="w-full p-4" />

    <!-- Step Content -->
    <div class="flex-grow overflow-auto">
      <div v-if="currentStep === 1">
        <WelcomeStep @next="nextStep" />
      </div>
      <div v-if="currentStep === 2">
        <AiDictionaryStep @prev="prevStep" @next="nextStep" />
      </div>
      <div v-if="currentStep === 3">
        <ContextualMeaningStep @prev="prevStep" @next="nextStep" />
      </div>
      <div v-if="currentStep === 4">
        <SaveWordsStep @prev="prevStep" @next="nextStep" />
      </div>
      <div v-if="currentStep === 5">
        <WritingAssistantStep @prev="prevStep" @next="nextStep" />
      </div>
       <div v-if="currentStep === 6">
        <GetStartedStep @prev="prevStep" @finish="finishOnboarding" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.onboarding-container {
  /* Add container styles */
}
</style> 
<script lang="ts" setup>
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
// @ts-ignore
import IconSearch from '@tabler/icons-vue/dist/esm/icons/IconSearch.mjs';
// @ts-ignore
import IconKeyboard from '@tabler/icons-vue/dist/esm/icons/IconKeyboard.mjs';
// @ts-ignore
import IconClick from '@tabler/icons-vue/dist/esm/icons/IconClick.mjs';
// @ts-ignore
import IconBrain from '@tabler/icons-vue/dist/esm/icons/IconBrain.mjs';
// @ts-ignore
import IconArrowLeft from '@tabler/icons-vue/dist/esm/icons/IconArrowLeft.mjs';
// @ts-ignore
import IconArrowRight from '@tabler/icons-vue/dist/esm/icons/IconArrowRight.mjs';

// Define emitted events
const emit = defineEmits(['prev', 'next']);

const goToPrevious = () => {
  emit('prev');
};

const goToNext = () => {
  emit('next');
};
</script>

<template>
  <div class="min-h-screen flex items-center justify-center px-6 py-12">
    <div class="max-w-5xl mx-auto">
      <!-- Header -->
      <div class="text-center mb-12">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl mb-6 shadow-lg">
          <IconBrain class="w-8 h-8 text-white" />
        </div>
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
          Meet Your AI-Powered Dictionary
        </h2>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">
          Instant definitions, examples, and explanations at your fingertips
        </p>
      </div>

      <!-- Main Content -->
      <div class="grid lg:grid-cols-2 gap-12 items-center mb-12">
        <!-- Visual Demo -->
        <div class="order-2 lg:order-1">
          <Card class="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200 shadow-xl">
            <CardContent class="p-8">
              <!-- Browser mockup -->
              <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <!-- Browser header -->
                <div class="bg-gray-100 px-4 py-2 flex items-center space-x-2">
                  <div class="flex space-x-1">
                    <div class="w-3 h-3 bg-red-400 rounded-full"></div>
                    <div class="w-3 h-3 bg-yellow-400 rounded-full"></div>
                    <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                  </div>
                  <div class="flex-1 bg-white rounded px-3 py-1 text-xs text-gray-500">
                    chrome-extension://pickvocab
                  </div>
                  <!-- Extension icon -->
                  <div class="w-6 h-6 bg-blue-600 rounded flex items-center justify-center">
                    <IconSearch class="w-3 h-3 text-white" />
                  </div>
                </div>

                <!-- Popup content -->
                <div class="p-6">
                  <div class="mb-4">
                    <div class="relative">
                      <input
                        type="text"
                        placeholder="Type any word, phrase, or idiom..."
                        class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        value="serendipity"
                        readonly
                      />
                      <IconSearch class="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                    </div>
                  </div>

                  <!-- Definition result -->
                  <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
                    <h4 class="font-semibold text-blue-900 mb-2">serendipity</h4>
                    <p class="text-sm text-blue-800 mb-2">
                      <em>/ˌserənˈdipədē/</em> • noun
                    </p>
                    <p class="text-sm text-gray-700">
                      The occurrence and development of events by chance in a happy or beneficial way.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <!-- Instructions -->
        <div class="order-1 lg:order-2 space-y-6">
          <div class="space-y-4">
            <!-- Step 1 -->
            <div class="flex items-start space-x-4 p-4 bg-white rounded-xl shadow-sm border border-gray-100">
              <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <IconClick class="w-4 h-4 text-blue-600" />
              </div>
              <div>
                <h4 class="font-semibold text-gray-900 mb-1">Click the Pickvocab icon</h4>
                <p class="text-sm text-gray-600">Find it in your browser toolbar</p>
              </div>
            </div>

            <!-- Step 2 -->
            <div class="flex items-start space-x-4 p-4 bg-white rounded-xl shadow-sm border border-gray-100">
              <div class="flex-shrink-0 w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                <IconKeyboard class="w-4 h-4 text-indigo-600" />
              </div>
              <div>
                <h4 class="font-semibold text-gray-900 mb-1">Or use keyboard shortcut</h4>
                <p class="text-sm text-gray-600">
                  Press <kbd class="px-2 py-1 bg-gray-100 rounded text-xs font-mono">Cmd+Shift+K</kbd> (Mac) /
                  <kbd class="px-2 py-1 bg-gray-100 rounded text-xs font-mono">Ctrl+Shift+K</kbd> (Windows)
                </p>
              </div>
            </div>

            <!-- Step 3 -->
            <div class="flex items-start space-x-4 p-4 bg-white rounded-xl shadow-sm border border-gray-100">
              <div class="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <IconSearch class="w-4 h-4 text-purple-600" />
              </div>
              <div>
                <h4 class="font-semibold text-gray-900 mb-1">Type your query</h4>
                <p class="text-sm text-gray-600">Any word, phrase, or idiom you want to understand</p>
              </div>
            </div>

            <!-- Step 4 -->
            <div class="flex items-start space-x-4 p-4 bg-white rounded-xl shadow-sm border border-gray-100">
              <div class="flex-shrink-0 w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <IconBrain class="w-4 h-4 text-green-600" />
              </div>
              <div>
                <h4 class="font-semibold text-gray-900 mb-1">Get AI-powered results</h4>
                <p class="text-sm text-gray-600">Clear definitions, examples, and more!</p>
              </div>
            </div>
          </div>

          <!-- Tip -->
          <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-200">
            <p class="text-sm text-blue-800">
              <strong>💡 Tip:</strong> Perfect for quick lookups without leaving your current tab.
            </p>
          </div>
        </div>
      </div>

      <!-- Navigation -->
      <div class="flex justify-between items-center">
        <Button
          @click="goToPrevious"
          variant="outline"
          size="lg"
          class="group"
        >
          <IconArrowLeft class="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform duration-300" />
          Previous
        </Button>

        <Button
          @click="goToNext"
          size="lg"
          class="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 group"
        >
          Next: Context Lookup
          <IconArrowRight class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
        </Button>
      </div>
    </div>
  </div>
</template>

<style scoped>
kbd {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
</style>
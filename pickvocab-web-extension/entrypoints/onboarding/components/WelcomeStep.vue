<script lang="ts" setup>
// Welcome Step Component

// Define emitted events
const emit = defineEmits(['next']);

const startOnboarding = () => {
  emit('next');
};

// Get the URL for the public asset using chrome.runtime.getURL
// This is the correct way to access web_accessible_resources
const pickvocabLogoUrl = chrome.runtime.getURL('pickvocab.svg');
</script>

<template>
  <div class="flex flex-col items-center justify-center h-full p-8 text-center bg-gray-50">
    <!-- Headline -->
    <h1 class="text-3xl md:text-4xl font-extrabold text-gray-900 mb-6">Welcome to Pickvocab! Your Smart English Companion.</h1>

    <!-- Image/Animation Placeholder -->
    <div class="mb-8">
      <!-- TODO: Replace this img tag with a friendly, welcoming graphic or animation (e.g., an SVG, GIF, or a Vue component for an animation) -->
      <img :src="pickvocabLogoUrl" alt="Pickvocab Logo" class="mx-auto h-40 w-40 object-contain">
    </div>

    <!-- Brief Intro Text -->
    <p class="text-base md:text-lg text-gray-700 leading-relaxed max-w-prose mb-10">
      Unlock a smarter way to learn and use English. Pickvocab helps you understand, save, and master new vocabulary effortlessly as you browse the web.
    </p>

    <!-- Button -->
    <div>
      <button
        class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg shadow-md transition duration-300 ease-in-out"
        @click="startOnboarding"
      >
        Let's Get Started!
      </button>
    </div>
  </div>
</template>

<style scoped>
/* Add any step-specific styles here if needed */
</style> 
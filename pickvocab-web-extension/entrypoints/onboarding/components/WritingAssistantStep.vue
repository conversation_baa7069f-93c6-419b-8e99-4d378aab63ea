<script lang="ts" setup>
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
// @ts-ignore
import IconPencil from '@tabler/icons-vue/dist/esm/icons/IconPencil.mjs';
// @ts-ignore
import IconCursorText from '@tabler/icons-vue/dist/esm/icons/IconCursorText.mjs';
// @ts-ignore
import IconClick from '@tabler/icons-vue/dist/esm/icons/IconClick.mjs';
// @ts-ignore
import IconSettings from '@tabler/icons-vue/dist/esm/icons/IconSettings.mjs';
// @ts-ignore
import IconWand from '@tabler/icons-vue/dist/esm/icons/IconWand.mjs';
// @ts-ignore
import IconArrowLeft from '@tabler/icons-vue/dist/esm/icons/IconArrowLeft.mjs';
// @ts-ignore
import IconArrowRight from '@tabler/icons-vue/dist/esm/icons/IconArrowRight.mjs';

// Define emitted events
const emit = defineEmits(['prev', 'next']);

const goToPrevious = () => {
  emit('prev');
};

const goToNext = () => {
  emit('next');
};
</script>

<template>
  <div class="min-h-screen flex items-center justify-center px-6 py-12">
    <div class="max-w-5xl mx-auto">
      <!-- Header -->
      <div class="text-center mb-12">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-orange-500 to-red-600 rounded-2xl mb-6 shadow-lg">
          <IconPencil class="w-8 h-8 text-white" />
        </div>
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
          Elevate Your Writing with AI Assistance
        </h2>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">
          Get intelligent suggestions to improve your writing style and use your saved vocabulary
        </p>
      </div>

      <!-- Main Content -->
      <div class="grid lg:grid-cols-2 gap-12 items-center mb-12">
        <!-- Visual Demo -->
        <div class="order-2 lg:order-1">
          <Card class="bg-gradient-to-br from-orange-50 to-red-50 border-orange-200 shadow-xl">
            <CardContent class="p-8">
              <!-- Writing interface mockup -->
              <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <!-- Header -->
                <div class="bg-gray-100 px-4 py-2 flex items-center space-x-2">
                  <div class="flex space-x-1">
                    <div class="w-3 h-3 bg-red-400 rounded-full"></div>
                    <div class="w-3 h-3 bg-yellow-400 rounded-full"></div>
                    <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                  </div>
                  <div class="flex-1 bg-white rounded px-3 py-1 text-xs text-gray-500">
                    compose.gmail.com
                  </div>
                </div>

                <!-- Email composer -->
                <div class="p-6">
                  <div class="mb-4">
                    <div class="text-xs text-gray-500 mb-2">To: <EMAIL></div>
                    <div class="text-xs text-gray-500 mb-4">Subject: Project Update</div>
                  </div>

                  <div class="border border-gray-200 rounded-lg p-4 min-h-[120px]">
                    <p class="text-sm text-gray-700 mb-3">
                      Hi there,
                    </p>
                    <p class="text-sm text-gray-700 mb-3">
                      <span class="bg-orange-200 px-1 rounded relative">
                        I wanted to give you a quick update on our project
                        <!-- Floating button -->
                        <div class="absolute -top-8 right-0">
                          <div class="w-6 h-6 bg-orange-600 rounded-full flex items-center justify-center shadow-lg">
                            <IconClick class="w-3 h-3 text-white" />
                          </div>
                        </div>
                      </span>
                      . We've made good progress.
                    </p>

                    <!-- Menu popup -->
                    <div class="relative mb-4">
                      <div class="absolute right-0 top-2 bg-white rounded-lg shadow-xl border border-gray-200 p-2 z-10">
                        <div class="space-y-1">
                          <div class="px-3 py-2 text-xs text-gray-500 rounded cursor-not-allowed">
                            📖 Lookup
                          </div>
                          <div class="px-3 py-2 text-xs bg-orange-50 text-orange-700 rounded hover:bg-orange-100 cursor-pointer font-medium">
                            ✏️ Fix writing
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Writing Assistant Modal -->
              <div class="mt-6 bg-white rounded-lg shadow-xl border border-gray-200 overflow-hidden">
                <div class="bg-gradient-to-r from-orange-500 to-red-600 px-4 py-3">
                  <h3 class="text-white font-semibold text-sm">Writing Assistant</h3>
                </div>

                <div class="p-4 space-y-4">
                  <!-- Settings -->
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                      <span class="text-xs text-gray-600">Tone:</span>
                      <select class="text-xs border border-gray-200 rounded px-2 py-1">
                        <option>Professional</option>
                      </select>
                    </div>
                    <div class="flex items-center space-x-2">
                      <input type="checkbox" id="vocab" class="w-3 h-3" checked />
                      <label for="vocab" class="text-xs text-gray-600">Use my vocabulary</label>
                    </div>
                  </div>

                  <!-- Revised text -->
                  <div class="bg-green-50 rounded-lg p-3 border border-green-200">
                    <p class="text-xs text-green-700 mb-1">Suggested revision:</p>
                    <p class="text-sm text-gray-700">
                      "I wanted to provide you with a comprehensive update on our project's progress."
                    </p>
                  </div>

                  <Button size="sm" class="w-full bg-orange-600 hover:bg-orange-700">
                    Apply Revision
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <!-- Instructions -->
        <div class="order-1 lg:order-2 space-y-6">
          <div class="space-y-4">
            <!-- Step 1 -->
            <div class="flex items-start space-x-4 p-4 bg-white rounded-xl shadow-sm border border-gray-100">
              <div class="flex-shrink-0 w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                <IconCursorText class="w-4 h-4 text-orange-600" />
              </div>
              <div>
                <h4 class="font-semibold text-gray-900 mb-1">Select your text</h4>
                <p class="text-sm text-gray-600">Highlight text in emails, documents, or social media posts</p>
              </div>
            </div>

            <!-- Step 2 -->
            <div class="flex items-start space-x-4 p-4 bg-white rounded-xl shadow-sm border border-gray-100">
              <div class="flex-shrink-0 w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                <IconClick class="w-4 h-4 text-red-600" />
              </div>
              <div>
                <h4 class="font-semibold text-gray-900 mb-1">Click the floating icon</h4>
                <p class="text-sm text-gray-600">The Pickvocab button appears near your selection</p>
              </div>
            </div>

            <!-- Step 3 -->
            <div class="flex items-start space-x-4 p-4 bg-white rounded-xl shadow-sm border border-gray-100">
              <div class="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <IconPencil class="w-4 h-4 text-purple-600" />
              </div>
              <div>
                <h4 class="font-semibold text-gray-900 mb-1">Choose "Fix writing"</h4>
                <p class="text-sm text-gray-600">Select the writing assistant option from the menu</p>
              </div>
            </div>

            <!-- Step 4 -->
            <div class="flex items-start space-x-4 p-4 bg-white rounded-xl shadow-sm border border-gray-100">
              <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <IconSettings class="w-4 h-4 text-blue-600" />
              </div>
              <div>
                <h4 class="font-semibold text-gray-900 mb-1">Customize settings</h4>
                <p class="text-sm text-gray-600">Adjust tone and enable vocabulary suggestions</p>
              </div>
            </div>

            <!-- Step 5 -->
            <div class="flex items-start space-x-4 p-4 bg-white rounded-xl shadow-sm border border-gray-100">
              <div class="flex-shrink-0 w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <IconWand class="w-4 h-4 text-green-600" />
              </div>
              <div>
                <h4 class="font-semibold text-gray-900 mb-1">Get AI improvements</h4>
                <p class="text-sm text-gray-600">Receive enhanced phrasing and grammar corrections</p>
              </div>
            </div>
          </div>

          <!-- Tip -->
          <div class="bg-gradient-to-r from-orange-50 to-red-50 rounded-xl p-4 border border-orange-200">
            <p class="text-sm text-orange-800">
              <strong>💡 Tip:</strong> The Writing Assistant helps you use your new words naturally and write with more confidence.
            </p>
          </div>
        </div>
      </div>

      <!-- Navigation -->
      <div class="flex justify-between items-center">
        <Button
          @click="goToPrevious"
          variant="outline"
          size="lg"
          class="group"
        >
          <IconArrowLeft class="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform duration-300" />
          Previous
        </Button>

        <Button
          @click="goToNext"
          size="lg"
          class="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 group"
        >
          Finish Setup
          <IconArrowRight class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
        </Button>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Add any step-specific styles here if needed */
</style>
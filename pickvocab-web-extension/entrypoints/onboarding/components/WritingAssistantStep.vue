<script lang="ts" setup>
// Writing Assistant Step Component

// Define emitted events
const emit = defineEmits(['prev', 'next']);

const goToPrevious = () => {
  emit('prev');
};

const goToNext = () => {
  emit('next');
};
</script>

<template>
  <div class="flex flex-col items-center justify-center h-full p-8 text-center bg-gray-50">
    <!-- Headline -->
    <h2 class="text-3xl md:text-4xl font-extrabold text-gray-900 mb-6">Elevate Your Writing with AI Assistance</h2>

    <!-- Visual Placeholder -->
    <div class="mb-8 w-full max-w-md bg-white rounded-lg shadow-md p-6 border border-gray-200 flex items-center justify-center min-h-[200px]">
      <!-- TODO: Replace this placeholder with a simplified static image or short GIF showing the writing assistant flow (select text -> floating icon -> click -> menu -> select Fix writing -> Assistant modal appears with options and revise button) -->
      <p class="text-gray-500">[Placeholder for Writing Assistant Visual]</p>
    </div>

    <!-- Instructional Text (Step-by-Step) -->
    <div class="text-left text-gray-700 leading-relaxed max-w-prose mb-8 space-y-3">
      <p>1. <strong>Select the text you're writing</strong> (e.g., an email, a document, a social media post).
        <span class="text-gray-500">(Highlight selected text in visual)</span>
      </p>
      <p>2. <strong>Click the Pickvocab icon</strong> that appears.
        <span class="text-gray-500">(Highlight floating icon)</span>
      </p>
      <p>3. Choose <strong>'Fix writing'</strong>.
        <span class="text-gray-500">(Highlight "Fix writing" option)</span>
      </p>
      <p>4. In the Assistant:</p>
      <ul class="list-disc list-inside ml-6 mb-4 space-y-2">
        <li>Adjust the <strong>Tone</strong> (e.g., Formal, Casual).
          <span class="text-gray-500">(Highlight Tone Selector)</span>
        </li>
        <li>Toggle <strong>'Use my vocabulary'</strong> to get suggestions from words you've saved.
          <span class="text-gray-500">(Highlight Vocab Toggle)</span>
        </li>
        <li>Click <strong>'Revise'</strong>.
          <span class="text-gray-500">(Highlight Revise button)</span>
        </li>
      </ul>
      <p>5. Get improved phrasing, grammar corrections, and see your learned vocabulary in action!
        <span class="text-gray-500">(Highlight revised text snippet)</span>
      </p>
    </div>

    <!-- Tip -->
    <p class="text-sm italic text-gray-600 mb-10">Tip: The Writing Assistant helps you use your new words naturally and write with more confidence.</p>

    <!-- Navigation Buttons -->
    <div>
      <button
        class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-6 rounded-lg shadow-md transition duration-300 ease-in-out mr-4"
        @click="goToPrevious"
      >
        Previous
      </button>
      <button
        class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-6 rounded-lg shadow-md transition duration-300 ease-in-out"
        @click="goToNext"
      >
        Next
      </button>
    </div>
  </div>
</template>

<style scoped>
/* Add any step-specific styles here if needed */
</style> 
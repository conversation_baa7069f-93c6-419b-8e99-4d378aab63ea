<script lang="ts" setup>
// Contextual Meaning Lookup Step Component

// Define emitted events
const emit = defineEmits(['prev', 'next']);

const goToPrevious = () => {
  emit('prev');
};

const goToNext = () => {
  emit('next');
};
</script>

<template>
  <div class="flex flex-col items-center justify-center h-full p-8 text-center bg-gray-50">
    <!-- Headline -->
    <h2 class="text-3xl md:text-4xl font-extrabold text-gray-900 mb-6">Understand Words in Their True Context</h2>

    <!-- Visual Placeholder -->
    <div class="mb-8 w-full max-w-md bg-white rounded-lg shadow-md p-6 border border-gray-200 flex items-center justify-center min-h-[200px]">
      <!-- TODO: Replace this placeholder with a simplified static image or short GIF showing the contextual lookup flow (select text -> floating icon -> click -> menu -> select Lookup -> definition popup) -->
      <p class="text-gray-500">[Placeholder for Contextual Meaning Lookup Visual]</p>
    </div>

    <!-- Instructional Text (Step-by-Step) -->
    <div class="text-left text-gray-700 leading-relaxed max-w-prose mb-8 space-y-3">
      <p>
        1. <strong>Select any text</strong> on a webpage.
        <span class="text-gray-500">(Highlight selected text in visual)</span>
      </p>
      <p>
        2. <strong>Click the Pickvocab icon</strong> that appears.
        <span class="text-gray-500">(Highlight floating icon in visual)</span>
      </p>
      <p>
        3. Choose <strong>'Lookup'</strong> from the menu.
        <span class="text-gray-500">(Highlight "Lookup" option in visual)</span>
      </p>
      <p>
        4. Instantly get an AI-powered explanation tailored to that specific sentence!
        <span class="text-gray-500">(Highlight definition popup in visual)</span>
      </p>
    </div>

    <!-- Alternative Trigger Text -->
    <p class="text-base text-gray-700 mb-8">
      Alternatively, select text and press <strong><code>Cmd+Shift+H</code></strong> (Mac) / <strong><code>Ctrl+Shift+H</code></strong> (Windows) for an even faster lookup.
    </p>

    <!-- Tip -->
    <p class="text-sm italic text-gray-600 mb-10">Tip: No more tab-switching! Understand complex phrases exactly as they're used.</p>

    <!-- Navigation Buttons -->
    <div>
      <button
        class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-6 rounded-lg shadow-md transition duration-300 ease-in-out mr-4"
        @click="goToPrevious"
      >
        Previous
      </button>
      <button
        class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-6 rounded-lg shadow-md transition duration-300 ease-in-out"
        @click="goToNext"
      >
        Next
      </button>
    </div>
  </div>
</template>

<style scoped>
/* Add any step-specific styles here if needed */
</style> 
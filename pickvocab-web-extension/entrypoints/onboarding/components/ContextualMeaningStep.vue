<script lang="ts" setup>
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
// @ts-ignore
import IconCursorText from '@tabler/icons-vue/dist/esm/icons/IconCursorText.mjs';
// @ts-ignore
import IconClick from '@tabler/icons-vue/dist/esm/icons/IconClick.mjs';
// @ts-ignore
import IconMenu2 from '@tabler/icons-vue/dist/esm/icons/IconMenu2.mjs';
// @ts-ignore
import IconBulb from '@tabler/icons-vue/dist/esm/icons/IconBulb.mjs';
// @ts-ignore
import IconKeyboard from '@tabler/icons-vue/dist/esm/icons/IconKeyboard.mjs';
// @ts-ignore
import IconArrowLeft from '@tabler/icons-vue/dist/esm/icons/IconArrowLeft.mjs';
// @ts-ignore
import IconArrowRight from '@tabler/icons-vue/dist/esm/icons/IconArrowRight.mjs';

// Define emitted events
const emit = defineEmits(['prev', 'next']);

const goToPrevious = () => {
  emit('prev');
};

const goToNext = () => {
  emit('next');
};
</script>

<template>
  <div class="min-h-screen flex items-center justify-center px-6 py-12">
    <div class="max-w-5xl mx-auto">
      <!-- Header -->
      <div class="text-center mb-12">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-600 rounded-2xl mb-6 shadow-lg">
          <IconCursorText class="w-8 h-8 text-white" />
        </div>
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
          Understand Words in Their True Context
        </h2>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">
          Get AI-powered explanations tailored to specific sentences and contexts
        </p>
      </div>

      <!-- Main Content -->
      <div class="grid lg:grid-cols-2 gap-12 items-center mb-12">
        <!-- Visual Demo -->
        <div class="order-2 lg:order-1">
          <Card class="bg-gradient-to-br from-purple-50 to-pink-50 border-purple-200 shadow-xl">
            <CardContent class="p-8">
              <!-- Webpage mockup -->
              <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <!-- Browser header -->
                <div class="bg-gray-100 px-4 py-2 flex items-center space-x-2">
                  <div class="flex space-x-1">
                    <div class="w-3 h-3 bg-red-400 rounded-full"></div>
                    <div class="w-3 h-3 bg-yellow-400 rounded-full"></div>
                    <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                  </div>
                  <div class="flex-1 bg-white rounded px-3 py-1 text-xs text-gray-500">
                    example.com/article
                  </div>
                </div>

                <!-- Article content -->
                <div class="p-6">
                  <h3 class="font-bold text-gray-900 mb-4">The Art of Communication</h3>
                  <div class="space-y-3 text-sm text-gray-700">
                    <p>
                      The speaker's words were quite
                      <span class="bg-blue-200 px-1 rounded relative">
                        eloquent and persuasive
                        <!-- Floating button -->
                        <div class="absolute -top-8 left-1/2 transform -translate-x-1/2">
                          <div class="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center shadow-lg">
                            <IconClick class="w-3 h-3 text-white" />
                          </div>
                        </div>
                      </span>
                      , capturing the audience's attention.
                    </p>

                    <!-- Menu popup -->
                    <div class="relative">
                      <div class="absolute left-20 top-2 bg-white rounded-lg shadow-xl border border-gray-200 p-2 z-10">
                        <div class="space-y-1">
                          <div class="px-3 py-2 text-xs bg-blue-50 text-blue-700 rounded hover:bg-blue-100 cursor-pointer font-medium">
                            📖 Lookup
                          </div>
                          <div class="px-3 py-2 text-xs text-gray-500 rounded cursor-not-allowed">
                            ✏️ Fix writing
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Definition popup -->
                  <div class="mt-6 bg-purple-50 rounded-lg p-4 border border-purple-200">
                    <h4 class="font-semibold text-purple-900 mb-2">Context Explanation</h4>
                    <p class="text-sm text-purple-800 mb-2">
                      <strong>"eloquent and persuasive"</strong> in this context means:
                    </p>
                    <p class="text-sm text-gray-700">
                      Speaking in a fluent, graceful manner that effectively convinces or influences the listeners. The combination suggests both beautiful expression and compelling argumentation.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <!-- Instructions -->
        <div class="order-1 lg:order-2 space-y-6">
          <div class="space-y-4">
            <!-- Step 1 -->
            <div class="flex items-start space-x-4 p-4 bg-white rounded-xl shadow-sm border border-gray-100">
              <div class="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <IconCursorText class="w-4 h-4 text-purple-600" />
              </div>
              <div>
                <h4 class="font-semibold text-gray-900 mb-1">Select any text</h4>
                <p class="text-sm text-gray-600">Highlight words or phrases on any webpage</p>
              </div>
            </div>

            <!-- Step 2 -->
            <div class="flex items-start space-x-4 p-4 bg-white rounded-xl shadow-sm border border-gray-100">
              <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <IconClick class="w-4 h-4 text-blue-600" />
              </div>
              <div>
                <h4 class="font-semibold text-gray-900 mb-1">Click the floating icon</h4>
                <p class="text-sm text-gray-600">The Pickvocab button appears near your selection</p>
              </div>
            </div>

            <!-- Step 3 -->
            <div class="flex items-start space-x-4 p-4 bg-white rounded-xl shadow-sm border border-gray-100">
              <div class="flex-shrink-0 w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                <IconMenu2 class="w-4 h-4 text-indigo-600" />
              </div>
              <div>
                <h4 class="font-semibold text-gray-900 mb-1">Choose "Lookup"</h4>
                <p class="text-sm text-gray-600">Select the lookup option from the menu</p>
              </div>
            </div>

            <!-- Step 4 -->
            <div class="flex items-start space-x-4 p-4 bg-white rounded-xl shadow-sm border border-gray-100">
              <div class="flex-shrink-0 w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <IconBulb class="w-4 h-4 text-green-600" />
              </div>
              <div>
                <h4 class="font-semibold text-gray-900 mb-1">Get contextual explanation</h4>
                <p class="text-sm text-gray-600">AI analyzes the specific sentence for accurate meaning</p>
              </div>
            </div>
          </div>

          <!-- Keyboard shortcut -->
          <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-4 border border-purple-200">
            <div class="flex items-start space-x-3">
              <IconKeyboard class="w-5 h-5 text-purple-600 mt-0.5" />
              <div>
                <h4 class="font-semibold text-purple-900 mb-1">Quick shortcut</h4>
                <p class="text-sm text-purple-800">
                  Select text and press <kbd class="px-2 py-1 bg-white rounded text-xs font-mono shadow">Cmd+Shift+H</kbd> (Mac) /
                  <kbd class="px-2 py-1 bg-white rounded text-xs font-mono shadow">Ctrl+Shift+H</kbd> (Windows)
                </p>
              </div>
            </div>
          </div>

          <!-- Tip -->
          <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-200">
            <p class="text-sm text-blue-800">
              <strong>💡 Tip:</strong> No more tab-switching! Understand complex phrases exactly as they're used.
            </p>
          </div>
        </div>
      </div>

      <!-- Navigation -->
      <div class="flex justify-between items-center">
        <Button
          @click="goToPrevious"
          variant="outline"
          size="lg"
          class="group"
        >
          <IconArrowLeft class="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform duration-300" />
          Previous
        </Button>

        <Button
          @click="goToNext"
          size="lg"
          class="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 group"
        >
          Next: Save Words
          <IconArrowRight class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
        </Button>
      </div>
    </div>
  </div>
</template>

<style scoped>
kbd {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
</style>
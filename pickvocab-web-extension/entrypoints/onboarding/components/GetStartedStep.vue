<script lang="ts" setup>
// Get Started Step Component

// Define emitted events
const emit = defineEmits(['prev', 'finish']);

const goToPrevious = () => {
  emit('prev');
};

const finishOnboarding = () => {
  emit('finish');
};

const openOptions = () => {
  chrome.runtime.openOptionsPage();
};
</script>

<template>
  <div class="flex flex-col items-center justify-center h-full p-8 text-center bg-gray-50">
    <!-- Headline -->
    <h2 class="text-3xl md:text-4xl font-extrabold text-gray-900 mb-6">You're All Set!</h2>

    <!-- Image/Animation Placeholder -->
    <div class="mb-8 w-full max-w-md bg-white rounded-lg shadow-md p-6 border border-gray-200 flex items-center justify-center min-h-[200px]">
      <!-- TODO: Replace this placeholder with a celebratory graphic or animation (e.g., checkmark, confetti) -->
      <p class="text-gray-500">[Placeholder for Celebration Visual]</p>
    </div>

    <!-- Text -->
    <p class="text-base md:text-lg text-gray-700 leading-relaxed max-w-prose mb-8">
      You're ready to make the most of Pickvocab! Start exploring, learning, and improving your English today.
    </p>

    <!-- Optional Links/Actions -->
    <div class="mb-10 flex justify-center space-x-6">
      <a href="https://pickvocab.com" target="_blank" class="text-blue-600 hover:text-blue-800 underline text-lg font-medium">Visit Pickvocab.com</a>
      <!-- TODO: Add link to Extension Options if needed -->
      <button class="text-blue-600 hover:text-blue-800 underline text-lg font-medium" @click="openOptions">Explore Extension Options</button>
    </div>

    <!-- Navigation Buttons -->
    <div>
      <button
        class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-6 rounded-lg shadow-md transition duration-300 ease-in-out mr-4"
        @click="goToPrevious"
      >
        Previous
      </button>
      <button
        class="bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-6 rounded-lg shadow-md transition duration-300 ease-in-out"
        @click="finishOnboarding"
      >
        Close Onboarding & Start Using Pickvocab
      </button>
    </div>
  </div>
</template>

<style scoped>
/* Add any step-specific styles here if needed */
</style> 
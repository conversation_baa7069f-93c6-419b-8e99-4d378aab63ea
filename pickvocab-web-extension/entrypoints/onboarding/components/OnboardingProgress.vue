<script lang="ts" setup>
import { defineProps } from 'vue';

const props = defineProps<{
  currentStep: number;
  totalSteps: number;
}>();

// Create an array for rendering dots
const stepsArray = Array.from({ length: props.totalSteps }, (_, i) => i + 1);
</script>

<template>
  <div class="flex items-center justify-center space-x-2">
    <!-- Progress Indicator Dots -->
    <div
      v-for="step in stepsArray"
      :key="step"
      class="w-3 h-3 rounded-full transition-colors duration-300"
      :class="{ 'bg-blue-600': step === props.currentStep, 'bg-gray-300': step !== props.currentStep }"
    ></div>
  </div>
</template>

<style scoped>
/* Add any step-specific styles here if needed */
</style> 
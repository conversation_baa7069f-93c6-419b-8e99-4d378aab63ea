<script lang="ts" setup>
// Click to Save Words Step Component

// Define emitted events
const emit = defineEmits(['prev', 'next']);

const goToPrevious = () => {
  emit('prev');
};

const goToNext = () => {
  emit('next');
};
</script>

<template>
  <div class="flex flex-col items-center justify-center h-full p-8 text-center bg-gray-50">
    <!-- Headline -->
    <h2 class="text-3xl md:text-4xl font-extrabold text-gray-900 mb-6">Build Your Personal Vocabulary Notebook</h2>

    <!-- Visual Placeholder -->
    <div class="mb-8 w-full max-w-md bg-white rounded-lg shadow-md p-6 border border-gray-200 flex items-center justify-center min-h-[200px]">
      <!-- TODO: Replace this placeholder with a simplified static image or short GIF showing the save word flow (definition popup with Save button -> click Save -> confirmation) -->
      <p class="text-gray-500">[Placeholder for Save Words Visual]</p>
    </div>

    <!-- Instructional Text (Step-by-Step) -->
    <div class="text-left text-gray-700 leading-relaxed max-w-prose mb-8 space-y-3">
      <p>
        1. After looking up a word (either via the <strong>popup dictionary</strong> or <strong>contextual lookup</strong>)...</p>
      <p>
        2. You'll see a <strong>'Save' button</strong>.
        <span class="text-gray-500">(Highlight "Save" button in visual)</span>
      </p>
      <p>
        3. <strong>Click it to add the word</strong>, its definition, and the original sentence (for contextual lookups)
        to your personal Pickvocab notebook.
      </p>
    </div>

    <!-- Login Requirement Note -->
    <p class="text-sm text-gray-600 mb-8">
      You'll need to be signed in to your Pickvocab account to save words. If you're not, we'll guide you through a quick sign-in/sign-up process.
    </p>

    <!-- Tip -->
    <p class="text-sm italic text-gray-600 mb-10">Tip: Saved words are stored with their rich context, making your review sessions super effective!</p>

    <!-- Navigation Buttons -->
    <div>
      <button
        class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-6 rounded-lg shadow-md transition duration-300 ease-in-out mr-4"
        @click="goToPrevious"
      >
        Previous
      </button>
      <button
        class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-6 rounded-lg shadow-md transition duration-300 ease-in-out"
        @click="goToNext"
      >
        Next
      </button>
    </div>
  </div>
</template>

<style scoped>
/* Add any step-specific styles here if needed */
</style> 